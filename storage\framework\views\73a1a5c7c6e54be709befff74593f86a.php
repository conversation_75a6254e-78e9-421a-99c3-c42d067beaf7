<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Daily Report - <?php echo e($dailyReport->unit->unit_code ?? 'N/A'); ?></title>
    <style>
        @page {
            margin: 15mm;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.2;
            color: #000;
        }

        .container {
            width: 100%;
            border: 2px solid #000;
            padding: 0;
            margin: 0;
        }

        /* Header Section */
        .header {
            border-bottom: 2px solid #000;
            padding: 8px;
            position: relative;
            height: 80px;
        }

        .header-left {
            position: absolute;
            left: 10px;
            top: 10px;
            width: 120px;
        }

        .logo {
            width: 60px;
            height: 60px;
            border: 1px solid #000;
            border-radius: 50%;
            background-color: #ff0000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 8px;
        }

        .company-name {
            font-size: 8px;
            font-weight: bold;
            margin-top: 2px;
            text-align: center;
        }

        .plant-dept {
            font-size: 7px;
            text-align: center;
        }

        .ppa-box {
            border: 1px solid #000;
            padding: 2px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            margin-top: 2px;
        }

        .header-center {
            text-align: center;
            margin: 0 140px;
            padding-top: 15px;
        }

        .main-title {
            font-size: 14px;
            font-weight: bold;
            margin: 0;
        }

        .sub-title {
            font-size: 10px;
            margin: 2px 0;
        }

        .header-right {
            position: absolute;
            right: 10px;
            top: 10px;
            width: 120px;
            text-align: center;
        }

        .mitra-kerja {
            font-size: 8px;
            margin-bottom: 5px;
        }

        .form-fields {
            border: 1px solid #000;
            padding: 5px;
            font-size: 8px;
        }

        .form-row {
            margin-bottom: 3px;
        }

        /* Data Unit Section */
        .data-unit {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .section-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 8px;
        }

        .data-grid {
            display: table;
            width: 100%;
        }

        .data-left, .data-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }

        .data-right {
            padding-left: 20px;
        }

        .field-row {
            margin-bottom: 5px;
            display: flex;
        }

        .field-label {
            width: 100px;
            font-size: 9px;
        }

        .field-colon {
            width: 10px;
        }

        .field-value {
            flex: 1;
            border-bottom: 1px solid #000;
            min-height: 12px;
            font-size: 9px;
        }

        /* Problem Issue Section */
        .problem-issue {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .text-area {
            border: 1px solid #000;
            min-height: 40px;
            padding: 5px;
            font-size: 9px;
        }

        /* Parts Failure Analysis Section */
        .parts-failure {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .analysis-area {
            border: 1px solid #000;
            min-height: 60px;
            padding: 5px;
            font-size: 9px;
        }

        /* Main Parts Problem Table */
        .parts-table-section {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .parts-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .parts-table th, .parts-table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            font-size: 9px;
        }

        .parts-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .parts-table td {
            height: 20px;
        }

        /* Picture Component Failure Section */
        .picture-failure {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .picture-area {
            border: 1px solid #000;
            min-height: 120px;
            padding: 5px;
            text-align: center;
        }

        .picture-area img {
            max-width: 100%;
            max-height: 110px;
            margin: 2px;
        }

        /* Correction Action Section */
        .correction-action {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .correction-area {
            border: 1px solid #000;
            min-height: 40px;
            padding: 5px;
            font-size: 9px;
        }

        /* Recommendation Section */
        .recommendation {
            border-bottom: 1px solid #000;
            padding: 8px;
        }

        .recommendation-area {
            border: 1px solid #000;
            min-height: 40px;
            padding: 5px;
            font-size: 9px;
        }

        /* Footer Signatures */
        .signatures {
            padding: 8px;
        }

        .signature-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .signature-table th, .signature-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 9px;
        }

        .signature-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        .signature-table .role {
            font-weight: bold;
            font-size: 8px;
        }

        .note {
            font-size: 7px;
            font-style: italic;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    PPA
                </div>
                <div class="company-name">PT. PUTRA PERKASA ABADI</div>
                <div class="plant-dept">PLANT DEPARTEMENT</div>
                <div class="ppa-box">PPA</div>
            </div>

            <div class="header-center">
                <div class="main-title">TECHNICAL ANALYSIS REPORT</div>
                <div class="sub-title">MITRA KERJA PT.PPA</div>
            </div>

            <div class="header-right">
                <div class="mitra-kerja">Perusahaan :</div>
                <div class="form-fields">
                    <div class="form-row">Bidang :</div>
                </div>
            </div>
        </div>

        <!-- Data Unit Section -->
        <div class="data-unit">
            <div class="section-title">Data Unit</div>
            <div class="data-grid">
                <div class="data-left">
                    <div class="field-row">
                        <span class="field-label">Unit Code</span>
                        <span class="field-colon">:</span>
                        <span class="field-value"><?php echo e($dailyReport->unit->unit_code ?? ''); ?></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Model Unit</span>
                        <span class="field-colon">:</span>
                        <span class="field-value"><?php echo e($dailyReport->unit->unit_type ?? ''); ?></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Hours Meter</span>
                        <span class="field-colon">:</span>
                        <span class="field-value"><?php echo e($dailyReport->hm ?? ''); ?></span>
                    </div>
                </div>
                <div class="data-right">
                    <div class="field-row">
                        <span class="field-label">Inspect by (Nama mekanik)</span>
                        <span class="field-colon">:</span>
                        <span class="field-value">
                            <?php if($dailyReport->technicians->count() > 0): ?>
                                <?php echo e($dailyReport->technicians->pluck('name')->implode(', ')); ?>

                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Component</span>
                        <span class="field-colon">:</span>
                        <span class="field-value"><?php echo e($dailyReport->problem_component ?? ''); ?></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Life Time Component</span>
                        <span class="field-colon">:</span>
                        <span class="field-value"></span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Trouble Date</span>
                        <span class="field-colon">:</span>
                        <span class="field-value">
                            <?php
                                $date = \Carbon\Carbon::parse($dailyReport->date_in);
                                $formattedDate = $date->format('d/m/Y');
                            ?>
                            <?php echo e($formattedDate); ?>

                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Problem Issue Section -->
        <div class="problem-issue">
            <div class="section-title">Problem Issue</div>
            <div class="text-area">
                <?php echo e($dailyReport->problem_description ?? ''); ?>

                <?php if($dailyReport->jobs->count() > 0): ?>
                    <br><br><strong>Job Description:</strong><br>
                    <?php $__currentLoopData = $dailyReport->jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        • <?php echo e($job->job_description); ?><br>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Parts Failure Analysis Section -->
        <div class="parts-failure">
            <div class="section-title">Parts Failure Analysis</div>
            <div class="analysis-area">
                <?php echo e($dailyReport->problem ?? ''); ?>

            </div>
        </div>

        <!-- Main Parts Problem Table -->
        <div class="parts-table-section">
            <div class="section-title">Main Parts Problem</div>
            <table class="parts-table">
                <thead>
                    <tr>
                        <th>PART NAME</th>
                        <th>PART NUMBER</th>
                        <th>QTY</th>
                        <th>REMARKS</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Empty rows for manual filling -->
                    <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                    <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                    <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                </tbody>
            </table>
        </div>

        <!-- Picture Component Failure Section -->
        <div class="picture-failure">
            <div class="section-title">Picture Component Failure</div>
            <div class="picture-area">
                <?php if($dailyReport->images->count() > 0): ?>
                    <?php $__currentLoopData = $dailyReport->images->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <img src="<?php echo e(public_path('assets/daily_reports/' . $image->image_path)); ?>" alt="Component Image">
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Correction Action Section -->
        <div class="correction-action">
            <div class="section-title">Correction Action <em>( Perbaikan segera yang bisa dilakukan )</em></div>
            <div class="correction-area">
                <?php if($dailyReport->jobs->count() > 0): ?>
                    <?php $__currentLoopData = $dailyReport->jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($job->highlight): ?>
                            <?php echo e($job->job_description); ?><br>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recommendation Preventive Action Section -->
        <div class="recommendation">
            <div class="section-title">Recommendation Preventive Action <em>( Saran perbaikan selanjutnya agar tidak terjadi lagi )</em></div>
            <div class="recommendation-area">
                <!-- This section can be filled manually or from additional data -->
            </div>
        </div>

        <!-- Footer Signatures -->
        <div class="signatures">
            <table class="signature-table">
                <thead>
                    <tr>
                        <th>Reporting by :</th>
                        <th>Checked by :</th>
                        <th>Approved by :</th>
                        <th>Review by :</th>
                        <th>Accepted by :</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="height: 60px;">&nbsp;</td>
                        <td style="height: 60px;">&nbsp;</td>
                        <td style="height: 60px;">&nbsp;</td>
                        <td style="height: 60px;">&nbsp;</td>
                        <td style="height: 60px;">&nbsp;</td>
                    </tr>
                    <tr>
                        <td class="role">MECHANIC</td>
                        <td class="role">GROUP LEADER PLANT</td>
                        <td class="role">P3O <em>(nama perusahaan)</em></td>
                        <td class="role">PLANT ENGINEER</td>
                        <td class="role">PLANNER</td>
                    </tr>
                </tbody>
            </table>
            <div class="note">
                <em>Note : Setiap penggunaan spare part dan komponen baru atau penggantian wajib membuat Technical Report</em>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/daily-reports/single-pdf.blade.php ENDPATH**/ ?>
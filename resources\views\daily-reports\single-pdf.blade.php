<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Daily Report - {{ $dailyReport->unit->unit_code ?? 'N/A' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header h2 {
            margin: 5px 0 0 0;
            font-size: 14px;
            color: #666;
        }
        .report-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-section {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 20px;
        }
        .info-row {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        .info-value {
            display: inline-block;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin: 20px 0 10px 0;
            color: #333;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .jobs-list, .technicians-list {
            margin: 10px 0;
        }
        .jobs-list ul, .technicians-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .jobs-list li, .technicians-list li {
            margin-bottom: 5px;
        }
        .highlighted-job {
            background-color: #225297;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .images-section {
            margin-top: 20px;
        }
        .images-grid {
            display: table;
            width: 100%;
        }
        .images-column {
            display: table-cell;
            width: 33.33%;
            vertical-align: top;
            padding-right: 15px;
        }
        .images-column:last-child {
            padding-right: 0;
        }
        .image-item {
            margin-bottom: 10px;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            max-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .image-caption {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        .dt-hours {
            font-weight: bold;
            color: #225297;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TECHNICAL ACTIVITY REPORT (TAR)</h1>
        <h2>Daily Report - {{ $dailyReport->unit->unit_code ?? 'N/A' }}</h2>
    </div>

    <div class="report-info">
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">Unit Code:</span>
                <span class="info-value">{{ $dailyReport->unit->unit_code ?? 'N/A' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Unit Type:</span>
                <span class="info-value">{{ $dailyReport->unit->unit_type ?? 'N/A' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Tanggal:</span>
                <span class="info-value">
                    @php
                        $date = \Carbon\Carbon::parse($dailyReport->date_in);
                        $months = [
                            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                        ];
                        $formattedDate = $date->day . ' ' . $months[$date->month] . ' ' . $date->year;
                    @endphp
                    {{ $formattedDate }}
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Jam Mulai:</span>
                <span class="info-value">{{ $dailyReport->hour_in }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Jam Selesai:</span>
                <span class="info-value">{{ $dailyReport->hour_out }}</span>
            </div>
        </div>
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">Shift:</span>
                <span class="info-value">{{ $dailyReport->shift }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">HM:</span>
                <span class="info-value">{{ $dailyReport->hm ?? 'N/A' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">DT Hours:</span>
                <span class="info-value dt-hours">
                    @php
                        try {
                            $start = \Carbon\Carbon::createFromFormat('H:i', $dailyReport->hour_in);
                            $end = \Carbon\Carbon::createFromFormat('H:i', $dailyReport->hour_out);
                            $diffInMinutes = $end->diffInMinutes($start);
                            if ($diffInMinutes < 0) {
                                $diffInMinutes += 24 * 60; // Handle overnight shifts
                            }
                        } catch (\Exception $e) {
                            $diffInMinutes = 0;
                        }
                    @endphp
                    {{ $diffInMinutes }} menit
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Problem:</span>
                <span class="info-value">{{ $dailyReport->problem ?? 'N/A' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Problem Component:</span>
                <span class="info-value">{{ $dailyReport->problem_component ?? 'N/A' }}</span>
            </div>
        </div>
    </div>

    @if($dailyReport->problem_description)
    <div class="section-title">Problem Description:</div>
    <div>{{ $dailyReport->problem_description }}</div>
    @endif

    @if($dailyReport->jobs->count() > 0)
    <div class="section-title">Job Description:</div>
    <div class="jobs-list">
        <ul>
            @foreach($dailyReport->jobs as $job)
            <li>
                @if($job->highlight)
                    <span class="highlighted-job">{{ $job->job_description }}</span>
                @else
                    {{ $job->job_description }}
                @endif
            </li>
            @endforeach
        </ul>
    </div>
    @endif

    @if($dailyReport->technicians->count() > 0)
    <div class="section-title">Man Power:</div>
    <div class="technicians-list">
        <ul>
            @foreach($dailyReport->technicians as $technician)
            <li>{{ $technician->name }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    @if($dailyReport->images->count() > 0)
    <div class="images-section">
        <div class="section-title">Dokumentasi:</div>
        <div class="images-grid">
            <div class="images-column">
                <strong>Gambar Sebelum:</strong>
                @foreach($dailyReport->images->where('type', 'before') as $image)
                <div class="image-item">
                    <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="Before Image">
                    <div class="image-caption">{{ $image->image_path }}</div>
                </div>
                @endforeach
            </div>
            <div class="images-column">
                <strong>Gambar Sesudah:</strong>
                @foreach($dailyReport->images->where('type', 'after') as $image)
                <div class="image-item">
                    <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="After Image">
                    <div class="image-caption">{{ $image->image_path }}</div>
                </div>
                @endforeach
            </div>
            <div class="images-column">
                <strong>Gambar Unit:</strong>
                @foreach($dailyReport->images->where('type', 'unit') as $image)
                <div class="image-item">
                    <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="Unit Image">
                    <div class="image-caption">{{ $image->image_path }}</div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <div class="footer">
        Generated on {{ now()->format('d/m/Y H:i:s') }}
    </div>
</body>
</html>
